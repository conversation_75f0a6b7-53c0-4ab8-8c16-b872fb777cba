Log started at 6/4/2025 8:49:28 PM
2025-06-04 20:49:28.127 [Information] LoggingService: Logging service initialized
2025-06-04 20:49:28.143 [Information] App: Verbose logging enabled
2025-06-04 20:49:28.146 [Information] AppConfigurationService: Initializing configuration service
2025-06-04 20:49:28.146 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Config
2025-06-04 20:49:28.147 [Information] AppConfigurationService: Configuration file not found, creating default
2025-06-04 20:49:28.152 [Warning] AppConfigurationService: Configuration service not initialized
2025-06-04 20:49:28.153 [Information] AppConfigurationService: Default configuration created
2025-06-04 20:49:28.153 [Information] AppConfigurationService: Configuration service initialized successfully
2025-06-04 20:49:28.154 [Information] App: Configuration service initialized successfully
2025-06-04 20:49:28.155 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-06-04 20:49:28.155 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-06-04 20:49:28.163 [Information] App: Environment variable exists: True, not 'false': False
2025-06-04 20:49:28.164 [Information] App: Final useDummyImplementations value: False
2025-06-04 20:49:28.164 [Information] App: Updating config to NOT use dummy implementations
2025-06-04 20:49:28.166 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-06-04 20:49:28.229 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Config\app_config.json
2025-06-04 20:49:28.231 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-06-04 20:49:28.231 [Information] App: usePatchedImplementation flag is: True
2025-06-04 20:49:28.232 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-06-04 20:49:28.232 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries'
2025-06-04 20:49:28.232 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-06-04 20:49:28.233 [Information] App: verboseLogging flag is: True
2025-06-04 20:49:28.239 [Information] App: Verifying real hardware requirements...
2025-06-04 20:49:28.239 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-06-04 20:49:28.240 [Information] App: ✓ Found critical library: apci.dll
2025-06-04 20:49:28.240 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-06-04 20:49:28.240 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-06-04 20:49:28.241 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 20:49:28.242 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-06-04 20:49:28.242 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Vocom\config.json
2025-06-04 20:49:28.243 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-06-04 20:49:28.254 [Information] App: *** ATTEMPTING TO CREATE PATCHED VOCOM SERVICE FACTORY ***
2025-06-04 20:49:28.255 [Information] App: Found PatchedVocomServiceFactory type: VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
2025-06-04 20:49:28.256 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-06-04 20:49:28.257 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
2025-06-04 20:49:28.258 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\VolvoFlashWR.Communication.dll
2025-06-04 20:49:28.288 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-06-04 20:49:28.289 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\patched_factory_created.txt
2025-06-04 20:49:28.290 [Information] App: Successfully created PatchedVocomServiceFactory instance using reflection
2025-06-04 20:49:28.290 [Information] App: Using VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory Vocom service factory
2025-06-04 20:49:28.290 [Information] App: Checking if PTT application is running before creating Vocom service
2025-06-04 20:49:28.344 [Information] App: Creating Vocom service (attempt 1/3)
2025-06-04 20:49:28.347 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-06-04 20:49:28.347 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-06-04 20:49:28.348 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-06-04 20:49:28.348 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-06-04 20:49:28.348 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-06-04 20:49:28.351 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-06-04 20:49:28.355 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-06-04 20:49:28.366 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-06-04 20:49:28.368 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-06-04 20:49:28.368 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-06-04 20:49:28.381 [Information] PhoenixVocomAdapter: Copied apci.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\apci.dll
2025-06-04 20:49:28.384 [Information] PhoenixVocomAdapter: Copied apci.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\apci.dll
2025-06-04 20:49:28.390 [Information] PhoenixVocomAdapter: Copied apcidb.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\apcidb.dll
2025-06-04 20:49:28.391 [Information] PhoenixVocomAdapter: Copied apcidb.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\apcidb.dll
2025-06-04 20:49:28.407 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Volvo.ApciPlus.dll
2025-06-04 20:49:28.411 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.ApciPlus.dll
2025-06-04 20:49:28.417 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Volvo.ApciPlusData.dll
2025-06-04 20:49:28.419 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.ApciPlusData.dll
2025-06-04 20:49:28.424 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Volvo.ApciPlusTea2Data.dll
2025-06-04 20:49:28.425 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.ApciPlusTea2Data.dll
2025-06-04 20:49:28.428 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interface.dll
2025-06-04 20:49:28.430 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.NAMS.AC.Services.Interface.dll
2025-06-04 20:49:28.433 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interfaces.dll
2025-06-04 20:49:28.435 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.NAMS.AC.Services.Interfaces.dll
2025-06-04 20:49:28.439 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Volvo.NVS.Core.dll
2025-06-04 20:49:28.440 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.NVS.Core.dll
2025-06-04 20:49:28.442 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Volvo.NVS.Logging.dll
2025-06-04 20:49:28.443 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.NVS.Logging.dll
2025-06-04 20:49:28.446 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Volvo.NVS.Persistence.dll
2025-06-04 20:49:28.447 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.NVS.Persistence.dll
2025-06-04 20:49:28.449 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Volvo.NVS.Persistence.NHibernate.dll
2025-06-04 20:49:28.450 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.NVS.Persistence.NHibernate.dll
2025-06-04 20:49:28.455 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\VolvoIt.Baf.Utility.dll
2025-06-04 20:49:28.457 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\VolvoIt.Baf.Utility.dll
2025-06-04 20:49:28.459 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-06-04 20:49:28.460 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-06-04 20:49:28.464 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\VolvoIt.Waf.ServiceContract.dll
2025-06-04 20:49:28.466 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\VolvoIt.Waf.ServiceContract.dll
2025-06-04 20:49:28.468 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\VolvoIt.Waf.Utility.dll
2025-06-04 20:49:28.469 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\VolvoIt.Waf.Utility.dll
2025-06-04 20:49:28.472 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Config\Volvo.ApciPlus.dll.config
2025-06-04 20:49:28.473 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.ApciPlus.dll.config
2025-06-04 20:49:28.477 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Config\Volvo.ApciPlusData.dll.config
2025-06-04 20:49:28.479 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.ApciPlusData.dll.config
2025-06-04 20:49:28.494 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Dependencies\NHibernate.dll
2025-06-04 20:49:28.498 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\NHibernate.dll
2025-06-04 20:49:28.503 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Dependencies\NHibernate.Caches.SysCache2.dll
2025-06-04 20:49:28.505 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\NHibernate.Caches.SysCache2.dll
2025-06-04 20:49:28.509 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Dependencies\Iesi.Collections.dll
2025-06-04 20:49:28.511 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Iesi.Collections.dll
2025-06-04 20:49:28.513 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Dependencies\Ionic.Zip.Reduced.dll
2025-06-04 20:49:28.515 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Ionic.Zip.Reduced.dll
2025-06-04 20:49:28.519 [Information] PhoenixVocomAdapter: Copied SharpCompress.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Dependencies\SharpCompress.dll
2025-06-04 20:49:28.525 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Dependencies\DotNetZip.dll
2025-06-04 20:49:28.527 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\DotNetZip.dll
2025-06-04 20:49:28.531 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Dependencies\ICSharpCode.SharpZipLib.dll
2025-06-04 20:49:28.532 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\ICSharpCode.SharpZipLib.dll
2025-06-04 20:49:28.534 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Vodia.CommonDomain.Model.dll
2025-06-04 20:49:28.535 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Vodia.CommonDomain.Model.dll
2025-06-04 20:49:28.540 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Vodia.Contracts.Common.dll
2025-06-04 20:49:28.542 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Vodia.Contracts.Common.dll
2025-06-04 20:49:28.545 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Vodia.UtilityComponent.dll
2025-06-04 20:49:28.546 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Vodia.UtilityComponent.dll
2025-06-04 20:49:28.550 [Information] PhoenixVocomAdapter: Copied log4net.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Dependencies\log4net.dll
2025-06-04 20:49:28.551 [Information] PhoenixVocomAdapter: Copied log4net.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\log4net.dll
2025-06-04 20:49:28.558 [Information] PhoenixVocomAdapter: Copied Newtonsoft.Json.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Dependencies\Newtonsoft.Json.dll
2025-06-04 20:49:28.562 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Dependencies\AutoMapper.dll
2025-06-04 20:49:28.564 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\AutoMapper.dll
2025-06-04 20:49:28.565 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-06-04 20:49:28.567 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.AppContext.dll
2025-06-04 20:49:28.569 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.AppContext.dll
2025-06-04 20:49:28.573 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Buffers.dll
2025-06-04 20:49:28.574 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Buffers.dll
2025-06-04 20:49:28.576 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Collections.Concurrent.dll
2025-06-04 20:49:28.578 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Collections.Concurrent.dll
2025-06-04 20:49:28.580 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Collections.dll
2025-06-04 20:49:28.581 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Collections.dll
2025-06-04 20:49:28.583 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Collections.NonGeneric.dll
2025-06-04 20:49:28.584 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Collections.NonGeneric.dll
2025-06-04 20:49:28.589 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Collections.Specialized.dll
2025-06-04 20:49:28.591 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Collections.Specialized.dll
2025-06-04 20:49:28.593 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.ComponentModel.dll
2025-06-04 20:49:28.594 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.ComponentModel.dll
2025-06-04 20:49:28.596 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.ComponentModel.EventBasedAsync.dll
2025-06-04 20:49:28.598 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.ComponentModel.EventBasedAsync.dll
2025-06-04 20:49:28.600 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.ComponentModel.Primitives.dll
2025-06-04 20:49:28.601 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.ComponentModel.Primitives.dll
2025-06-04 20:49:28.605 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.ComponentModel.TypeConverter.dll
2025-06-04 20:49:28.606 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.ComponentModel.TypeConverter.dll
2025-06-04 20:49:28.609 [Information] PhoenixVocomAdapter: Copied System.Console.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Console.dll
2025-06-04 20:49:28.610 [Information] PhoenixVocomAdapter: Copied System.Console.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Console.dll
2025-06-04 20:49:28.612 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Data.Common.dll
2025-06-04 20:49:28.614 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Data.Common.dll
2025-06-04 20:49:28.617 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Data.SQLite.dll
2025-06-04 20:49:28.619 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Data.SQLite.dll
2025-06-04 20:49:28.623 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Data.SqlServerCe.dll
2025-06-04 20:49:28.625 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Data.SqlServerCe.dll
2025-06-04 20:49:28.627 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Diagnostics.Contracts.dll
2025-06-04 20:49:28.628 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Diagnostics.Contracts.dll
2025-06-04 20:49:28.630 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Diagnostics.Debug.dll
2025-06-04 20:49:28.647 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Diagnostics.Debug.dll
2025-06-04 20:49:28.652 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Diagnostics.FileVersionInfo.dll
2025-06-04 20:49:28.653 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Diagnostics.FileVersionInfo.dll
2025-06-04 20:49:28.656 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Diagnostics.Process.dll
2025-06-04 20:49:28.658 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Diagnostics.Process.dll
2025-06-04 20:49:28.660 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Diagnostics.StackTrace.dll
2025-06-04 20:49:28.661 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Diagnostics.StackTrace.dll
2025-06-04 20:49:28.663 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Diagnostics.TextWriterTraceListener.dll
2025-06-04 20:49:28.665 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Diagnostics.TextWriterTraceListener.dll
2025-06-04 20:49:28.667 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Diagnostics.Tools.dll
2025-06-04 20:49:28.668 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Diagnostics.Tools.dll
2025-06-04 20:49:28.671 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Diagnostics.TraceSource.dll
2025-06-04 20:49:28.672 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Diagnostics.TraceSource.dll
2025-06-04 20:49:28.674 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Diagnostics.Tracing.dll
2025-06-04 20:49:28.676 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Diagnostics.Tracing.dll
2025-06-04 20:49:28.678 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Drawing.Primitives.dll
2025-06-04 20:49:28.679 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Drawing.Primitives.dll
2025-06-04 20:49:28.682 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Dynamic.Runtime.dll
2025-06-04 20:49:28.683 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Dynamic.Runtime.dll
2025-06-04 20:49:28.686 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Globalization.Calendars.dll
2025-06-04 20:49:28.687 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Globalization.Calendars.dll
2025-06-04 20:49:28.690 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Globalization.dll
2025-06-04 20:49:28.691 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Globalization.dll
2025-06-04 20:49:28.693 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Globalization.Extensions.dll
2025-06-04 20:49:28.695 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Globalization.Extensions.dll
2025-06-04 20:49:28.697 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.Compression.dll
2025-06-04 20:49:28.699 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.Compression.dll
2025-06-04 20:49:28.701 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.Compression.ZipFile.dll
2025-06-04 20:49:28.702 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.Compression.ZipFile.dll
2025-06-04 20:49:28.706 [Information] PhoenixVocomAdapter: Copied System.IO.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.dll
2025-06-04 20:49:28.707 [Information] PhoenixVocomAdapter: Copied System.IO.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.dll
2025-06-04 20:49:28.710 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.FileSystem.dll
2025-06-04 20:49:28.712 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.FileSystem.dll
2025-06-04 20:49:28.714 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.FileSystem.DriveInfo.dll
2025-06-04 20:49:28.715 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.FileSystem.DriveInfo.dll
2025-06-04 20:49:28.717 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.FileSystem.Primitives.dll
2025-06-04 20:49:28.719 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.FileSystem.Primitives.dll
2025-06-04 20:49:28.723 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.FileSystem.Watcher.dll
2025-06-04 20:49:28.724 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.FileSystem.Watcher.dll
2025-06-04 20:49:28.726 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.IsolatedStorage.dll
2025-06-04 20:49:28.728 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.IsolatedStorage.dll
2025-06-04 20:49:28.729 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.MemoryMappedFiles.dll
2025-06-04 20:49:28.731 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.MemoryMappedFiles.dll
2025-06-04 20:49:28.733 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.Pipes.dll
2025-06-04 20:49:28.734 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.Pipes.dll
2025-06-04 20:49:28.739 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.UnmanagedMemoryStream.dll
2025-06-04 20:49:28.740 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.UnmanagedMemoryStream.dll
2025-06-04 20:49:28.742 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Linq.dll
2025-06-04 20:49:28.744 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Linq.dll
2025-06-04 20:49:28.745 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Linq.Expressions.dll
2025-06-04 20:49:28.747 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Linq.Expressions.dll
2025-06-04 20:49:28.749 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Linq.Parallel.dll
2025-06-04 20:49:28.750 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Linq.Parallel.dll
2025-06-04 20:49:28.752 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Linq.Queryable.dll
2025-06-04 20:49:28.754 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Linq.Queryable.dll
2025-06-04 20:49:28.757 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Memory.dll
2025-06-04 20:49:28.758 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Memory.dll
2025-06-04 20:49:28.762 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.Http.dll
2025-06-04 20:49:28.764 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.Http.dll
2025-06-04 20:49:28.768 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.NameResolution.dll
2025-06-04 20:49:28.770 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.NameResolution.dll
2025-06-04 20:49:28.774 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.NetworkInformation.dll
2025-06-04 20:49:28.775 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.NetworkInformation.dll
2025-06-04 20:49:28.783 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.Ping.dll
2025-06-04 20:49:28.785 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.Ping.dll
2025-06-04 20:49:28.789 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.Primitives.dll
2025-06-04 20:49:28.790 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.Primitives.dll
2025-06-04 20:49:28.793 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.Requests.dll
2025-06-04 20:49:28.795 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.Requests.dll
2025-06-04 20:49:28.796 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.Security.dll
2025-06-04 20:49:28.798 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.Security.dll
2025-06-04 20:49:28.800 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.Sockets.dll
2025-06-04 20:49:28.801 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.Sockets.dll
2025-06-04 20:49:28.805 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.WebHeaderCollection.dll
2025-06-04 20:49:28.806 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.WebHeaderCollection.dll
2025-06-04 20:49:28.808 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.WebSockets.Client.dll
2025-06-04 20:49:28.810 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.WebSockets.Client.dll
2025-06-04 20:49:28.812 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.WebSockets.dll
2025-06-04 20:49:28.813 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.WebSockets.dll
2025-06-04 20:49:28.816 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Numerics.Vectors.dll
2025-06-04 20:49:28.817 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Numerics.Vectors.dll
2025-06-04 20:49:28.819 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.ObjectModel.dll
2025-06-04 20:49:28.822 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.ObjectModel.dll
2025-06-04 20:49:28.824 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Reflection.dll
2025-06-04 20:49:28.825 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Reflection.dll
2025-06-04 20:49:28.828 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Reflection.Extensions.dll
2025-06-04 20:49:28.829 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Reflection.Extensions.dll
2025-06-04 20:49:28.831 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Reflection.Primitives.dll
2025-06-04 20:49:28.833 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Reflection.Primitives.dll
2025-06-04 20:49:28.835 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Resources.Reader.dll
2025-06-04 20:49:28.836 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Resources.Reader.dll
2025-06-04 20:49:28.841 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Resources.ResourceManager.dll
2025-06-04 20:49:28.842 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Resources.ResourceManager.dll
2025-06-04 20:49:28.844 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Resources.Writer.dll
2025-06-04 20:49:28.845 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Resources.Writer.dll
2025-06-04 20:49:28.847 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.CompilerServices.Unsafe.dll
2025-06-04 20:49:28.848 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.CompilerServices.Unsafe.dll
2025-06-04 20:49:28.850 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.CompilerServices.VisualC.dll
2025-06-04 20:49:28.851 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.CompilerServices.VisualC.dll
2025-06-04 20:49:28.853 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.dll
2025-06-04 20:49:28.855 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.dll
2025-06-04 20:49:28.857 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.Extensions.dll
2025-06-04 20:49:28.858 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.Extensions.dll
2025-06-04 20:49:28.860 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.Handles.dll
2025-06-04 20:49:28.861 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.Handles.dll
2025-06-04 20:49:28.864 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.InteropServices.dll
2025-06-04 20:49:28.865 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.InteropServices.dll
2025-06-04 20:49:28.868 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.InteropServices.RuntimeInformation.dll
2025-06-04 20:49:28.869 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.InteropServices.RuntimeInformation.dll
2025-06-04 20:49:28.872 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.Numerics.dll
2025-06-04 20:49:28.874 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.Numerics.dll
2025-06-04 20:49:28.876 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Formatters.dll
2025-06-04 20:49:28.878 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.Serialization.Formatters.dll
2025-06-04 20:49:28.880 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Json.dll
2025-06-04 20:49:28.882 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.Serialization.Json.dll
2025-06-04 20:49:28.884 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Primitives.dll
2025-06-04 20:49:28.885 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.Serialization.Primitives.dll
2025-06-04 20:49:28.889 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Xml.dll
2025-06-04 20:49:28.890 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.Serialization.Xml.dll
2025-06-04 20:49:28.892 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Security.Claims.dll
2025-06-04 20:49:28.894 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Security.Claims.dll
2025-06-04 20:49:28.896 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Security.Cryptography.Algorithms.dll
2025-06-04 20:49:28.898 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Security.Cryptography.Algorithms.dll
2025-06-04 20:49:28.899 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Security.Cryptography.Csp.dll
2025-06-04 20:49:28.901 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Security.Cryptography.Csp.dll
2025-06-04 20:49:28.905 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Security.Cryptography.Encoding.dll
2025-06-04 20:49:28.906 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Security.Cryptography.Encoding.dll
2025-06-04 20:49:28.908 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Security.Cryptography.Primitives.dll
2025-06-04 20:49:28.910 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Security.Cryptography.Primitives.dll
2025-06-04 20:49:28.912 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Security.Cryptography.X509Certificates.dll
2025-06-04 20:49:28.913 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Security.Cryptography.X509Certificates.dll
2025-06-04 20:49:28.915 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Security.Principal.dll
2025-06-04 20:49:28.916 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Security.Principal.dll
2025-06-04 20:49:28.919 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Security.SecureString.dll
2025-06-04 20:49:28.921 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Security.SecureString.dll
2025-06-04 20:49:28.923 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Text.Encoding.dll
2025-06-04 20:49:28.925 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Text.Encoding.dll
2025-06-04 20:49:28.927 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Text.Encoding.Extensions.dll
2025-06-04 20:49:28.928 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Text.Encoding.Extensions.dll
2025-06-04 20:49:28.931 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Text.RegularExpressions.dll
2025-06-04 20:49:28.932 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Text.RegularExpressions.dll
2025-06-04 20:49:28.934 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Threading.dll
2025-06-04 20:49:28.936 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Threading.dll
2025-06-04 20:49:28.939 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Threading.Overlapped.dll
2025-06-04 20:49:28.941 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Threading.Overlapped.dll
2025-06-04 20:49:28.943 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Threading.Tasks.dll
2025-06-04 20:49:28.944 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Threading.Tasks.dll
2025-06-04 20:49:28.946 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Threading.Tasks.Extensions.dll
2025-06-04 20:49:28.947 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Threading.Tasks.Extensions.dll
2025-06-04 20:49:28.949 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Threading.Tasks.Parallel.dll
2025-06-04 20:49:28.951 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Threading.Tasks.Parallel.dll
2025-06-04 20:49:28.954 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Threading.Thread.dll
2025-06-04 20:49:28.956 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Threading.Thread.dll
2025-06-04 20:49:28.958 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Threading.ThreadPool.dll
2025-06-04 20:49:28.959 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Threading.ThreadPool.dll
2025-06-04 20:49:28.961 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Threading.Timer.dll
2025-06-04 20:49:28.963 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Threading.Timer.dll
2025-06-04 20:49:28.965 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.ValueTuple.dll
2025-06-04 20:49:28.966 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.ValueTuple.dll
2025-06-04 20:49:28.969 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Xml.ReaderWriter.dll
2025-06-04 20:49:28.970 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Xml.ReaderWriter.dll
2025-06-04 20:49:28.973 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Xml.XDocument.dll
2025-06-04 20:49:28.974 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Xml.XDocument.dll
2025-06-04 20:49:28.976 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Xml.XmlDocument.dll
2025-06-04 20:49:28.977 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Xml.XmlDocument.dll
2025-06-04 20:49:28.979 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Xml.XmlSerializer.dll
2025-06-04 20:49:28.980 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Xml.XmlSerializer.dll
2025-06-04 20:49:28.982 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Xml.XPath.dll
2025-06-04 20:49:28.984 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Xml.XPath.dll
2025-06-04 20:49:28.988 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Xml.XPath.XDocument.dll
2025-06-04 20:49:28.990 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Xml.XPath.XDocument.dll
2025-06-04 20:49:28.992 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\SystemInterface.dll
2025-06-04 20:49:28.993 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\SystemInterface.dll
2025-06-04 20:49:28.994 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-06-04 20:49:28.995 [Information] PhoenixVocomAdapter: Loading APCI library dynamically
2025-06-04 20:49:29.140 [Error] PhoenixVocomAdapter: Failed to load APCI library. Error code: 193
2025-06-04 20:49:29.141 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-06-04 20:49:29.141 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-06-04 20:49:29.143 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-06-04 20:49:29.143 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-06-04 20:49:29.145 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-06-04 20:49:29.147 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-06-04 20:49:29.149 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-06-04 20:49:29.151 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 20:49:29.151 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-06-04 20:49:29.152 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-06-04 20:49:29.154 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-06-04 20:49:29.155 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-06-04 20:49:29.156 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-06-04 20:49:29.156 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-06-04 20:49:29.156 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-06-04 20:49:29.156 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-06-04 20:49:29.157 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-06-04 20:49:29.158 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-06-04 20:49:29.165 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-06-04 20:49:29.172 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-06-04 20:49:29.175 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-06-04 20:49:29.178 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-06-04 20:49:29.179 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-06-04 20:49:29.180 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-06-04 20:49:29.180 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-06-04 20:49:29.181 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-06-04 20:49:29.182 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-06-04 20:49:29.184 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-06-04 20:49:29.187 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-06-04 20:49:29.188 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-06-04 20:49:29.552 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-06-04 20:49:29.552 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-06-04 20:49:29.553 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\apci.dll
2025-06-04 20:49:29.553 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\apci.dll
2025-06-04 20:49:29.556 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-06-04 20:49:29.557 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-06-04 20:49:29.691 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-06-04 20:49:29.692 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-06-04 20:49:29.780 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-06-04 20:49:29.871 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-06-04 20:49:29.871 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-06-04 20:49:30.199 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-06-04 20:49:30.199 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-06-04 20:49:30.345 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-06-04 20:49:30.346 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-06-04 20:49:30.499 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-06-04 20:49:30.649 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-06-04 20:49:30.649 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-06-04 20:49:30.817 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-06-04 20:49:30.965 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-06-04 20:49:30.966 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-06-04 20:49:31.086 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\
2025-06-04 20:49:31.296 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\
2025-06-04 20:49:31.447 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-06-04 20:49:31.631 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-06-04 20:49:31.632 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-06-04 20:49:31.724 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries
2025-06-04 20:49:32.125 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-06-04 20:49:32.126 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-06-04 20:49:32.260 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-06-04 20:49:32.392 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-06-04 20:49:32.393 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-06-04 20:49:32.638 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-06-04 20:49:32.887 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-06-04 20:49:32.888 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-06-04 20:49:33.090 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\
2025-06-04 20:49:33.299 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\
2025-06-04 20:49:33.382 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\
2025-06-04 20:49:33.386 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-06-04 20:49:33.386 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\apci.dll
2025-06-04 20:49:33.387 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-06-04 20:49:33.388 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-06-04 20:49:33.389 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-06-04 20:49:33.391 [Information] VocomDriver: Initializing Vocom driver
2025-06-04 20:49:33.394 [Information] VocomNativeInterop: Initializing Vocom driver
2025-06-04 20:49:33.398 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-06-04 20:49:33.398 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 20:49:33.399 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 20:49:33.400 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 20:49:33.401 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-06-04 20:49:33.560 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\msvcr120.dll
2025-06-04 20:49:33.627 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\msvcp120.dll
2025-06-04 20:49:33.629 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-06-04 20:49:33.703 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\msvcp140.dll
2025-06-04 20:49:33.763 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\vcruntime140.dll
2025-06-04 20:49:33.764 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-06-04 20:49:33.769 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-06-04 20:49:33.773 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-06-04 20:49:33.776 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-06-04 20:49:33.777 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-06-04 20:49:33.777 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-06-04 20:49:33.778 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-06-04 20:49:33.778 [Information] VocomDriver: Vocom driver initialized successfully
2025-06-04 20:49:33.784 [Information] VocomService: Initializing Vocom service with dependencies
2025-06-04 20:49:33.785 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-06-04 20:49:33.787 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-06-04 20:49:33.788 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-06-04 20:49:33.890 [Information] WiFiCommunicationService: WiFi is available
2025-06-04 20:49:33.891 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-06-04 20:49:33.893 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-06-04 20:49:33.894 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-06-04 20:49:33.896 [Information] BluetoothCommunicationService: Bluetooth is available
2025-06-04 20:49:33.897 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-06-04 20:49:33.899 [Information] VocomService: Initializing Vocom service
2025-06-04 20:49:33.902 [Information] VocomService: Checking if PTT application is running
2025-06-04 20:49:33.917 [Information] VocomService: PTT application is not running
2025-06-04 20:49:33.919 [Debug] VocomService: Checking if Bluetooth is enabled
2025-06-04 20:49:33.922 [Debug] VocomService: Bluetooth is enabled
2025-06-04 20:49:33.924 [Information] VocomService: Vocom service initialized successfully
2025-06-04 20:49:33.924 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-06-04 20:49:33.924 [Information] App: Initializing Vocom service
2025-06-04 20:49:33.925 [Information] VocomService: Initializing Vocom service
2025-06-04 20:49:33.925 [Information] VocomService: Checking if PTT application is running
2025-06-04 20:49:33.938 [Information] VocomService: PTT application is not running
2025-06-04 20:49:33.939 [Debug] VocomService: Checking if Bluetooth is enabled
2025-06-04 20:49:33.940 [Debug] VocomService: Bluetooth is enabled
2025-06-04 20:49:33.940 [Information] VocomService: Vocom service initialized successfully
2025-06-04 20:49:33.943 [Information] VocomService: Scanning for Vocom devices
2025-06-04 20:49:34.147 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 20:49:34.182 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 20:49:34.183 [Debug] VocomService: Checking if Bluetooth is enabled
2025-06-04 20:49:34.183 [Debug] VocomService: Bluetooth is enabled
2025-06-04 20:49:34.187 [Debug] VocomService: Checking if WiFi is available
2025-06-04 20:49:34.189 [Debug] VocomService: WiFi is available
2025-06-04 20:49:34.190 [Information] VocomService: Found 2 Vocom devices
2025-06-04 20:49:34.191 [Information] App: Found 2 Vocom devices, attempting to connect to the first one
2025-06-04 20:49:34.195 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 20:49:34.195 [Information] VocomService: Checking if PTT application is running
2025-06-04 20:49:34.210 [Information] VocomService: PTT application is not running
2025-06-04 20:49:34.213 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 20:49:34.213 [Debug] VocomService: Checking if Bluetooth is enabled
2025-06-04 20:49:34.214 [Debug] VocomService: Bluetooth is enabled
2025-06-04 20:49:34.214 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-06-04 20:49:35.020 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 20:49:35.022 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 20:49:35.022 [Information] App: Connected to Vocom device Vocom - 88890300 (Bluetooth)
2025-06-04 20:49:35.029 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-06-04 20:49:35.032 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:49:35.033 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-06-04 20:49:35.037 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 20:49:35.039 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 20:49:35.040 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 20:49:35.044 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 20:49:35.046 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 20:49:35.067 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 20:49:35.070 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 20:49:35.074 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 20:49:35.084 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 20:49:35.087 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 20:49:35.100 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 20:49:35.101 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 20:49:35.102 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 20:49:35.102 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 20:49:35.102 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 20:49:35.103 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 20:49:35.103 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 20:49:35.103 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 20:49:35.104 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 20:49:35.109 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 20:49:35.109 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 20:49:35.109 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 20:49:35.110 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 20:49:35.110 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 20:49:35.110 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 20:49:35.111 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 20:49:35.111 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 20:49:35.115 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 20:49:35.122 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-06-04 20:49:35.123 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 20:49:35.128 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 20:49:35.131 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:49:35.138 [Information] CANRegisterAccess: Read value 0xE6 from register 0x0141 (simulated)
2025-06-04 20:49:35.146 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:49:35.152 [Information] CANRegisterAccess: Read value 0xA1 from register 0x0141 (simulated)
2025-06-04 20:49:35.152 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-06-04 20:49:35.153 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-06-04 20:49:35.154 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-06-04 20:49:35.160 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-06-04 20:49:35.160 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-06-04 20:49:35.166 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-06-04 20:49:35.166 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-06-04 20:49:35.167 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-06-04 20:49:35.174 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-06-04 20:49:35.174 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-06-04 20:49:35.174 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-06-04 20:49:35.180 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-06-04 20:49:35.180 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-06-04 20:49:35.186 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-06-04 20:49:35.186 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-06-04 20:49:35.192 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-06-04 20:49:35.192 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-06-04 20:49:35.198 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-06-04 20:49:35.198 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-06-04 20:49:35.204 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-06-04 20:49:35.204 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-06-04 20:49:35.210 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-06-04 20:49:35.210 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-06-04 20:49:35.216 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-06-04 20:49:35.216 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-06-04 20:49:35.222 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-06-04 20:49:35.223 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-06-04 20:49:35.229 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-06-04 20:49:35.229 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-06-04 20:49:35.235 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-06-04 20:49:35.235 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-06-04 20:49:35.241 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-06-04 20:49:35.241 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-06-04 20:49:35.248 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-06-04 20:49:35.248 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-06-04 20:49:35.254 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-06-04 20:49:35.255 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-06-04 20:49:35.261 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-06-04 20:49:35.261 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-06-04 20:49:35.267 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-06-04 20:49:35.267 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-06-04 20:49:35.273 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-06-04 20:49:35.273 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-06-04 20:49:35.280 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-06-04 20:49:35.280 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-06-04 20:49:35.281 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-06-04 20:49:35.287 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-06-04 20:49:35.287 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-06-04 20:49:35.288 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-06-04 20:49:35.288 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:49:35.294 [Information] CANRegisterAccess: Read value 0xAC from register 0x0141 (simulated)
2025-06-04 20:49:35.294 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-06-04 20:49:35.295 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-06-04 20:49:35.295 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-06-04 20:49:35.295 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-06-04 20:49:35.301 [Information] CANRegisterAccess: Read value 0x85 from register 0x0140 (simulated)
2025-06-04 20:49:35.307 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-06-04 20:49:35.313 [Information] CANRegisterAccess: Read value 0x8B from register 0x0140 (simulated)
2025-06-04 20:49:35.319 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-06-04 20:49:35.325 [Information] CANRegisterAccess: Read value 0xAC from register 0x0140 (simulated)
2025-06-04 20:49:35.331 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-06-04 20:49:35.337 [Information] CANRegisterAccess: Read value 0x59 from register 0x0140 (simulated)
2025-06-04 20:49:35.337 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-06-04 20:49:35.338 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 20:49:35.342 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 20:49:35.342 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 20:49:35.353 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 20:49:35.354 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 20:49:35.354 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 20:49:35.362 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:49:35.362 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-06-04 20:49:35.414 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-06-04 20:49:35.416 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 20:49:35.416 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 20:49:35.419 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 20:49:35.419 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 20:49:35.430 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 20:49:35.431 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 20:49:35.431 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 20:49:35.442 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 20:49:35.453 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 20:49:35.464 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 20:49:35.475 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 20:49:35.486 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 20:49:35.490 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 20:49:35.490 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 20:49:35.502 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 20:49:35.503 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 20:49:35.503 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 20:49:35.515 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 20:49:35.526 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 20:49:35.537 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 20:49:35.548 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 20:49:35.559 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 20:49:35.570 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 20:49:35.574 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 20:49:35.575 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 20:49:35.586 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 20:49:35.588 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 20:49:35.588 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 20:49:35.589 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 20:49:35.589 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 20:49:35.590 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 20:49:35.590 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 20:49:35.590 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 20:49:35.591 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 20:49:35.591 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 20:49:35.591 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 20:49:35.591 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 20:49:35.592 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 20:49:35.592 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 20:49:35.592 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 20:49:35.593 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 20:49:35.593 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 20:49:35.694 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 20:49:35.694 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 20:49:35.698 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 20:49:35.700 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:49:35.700 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 20:49:35.701 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 20:49:35.701 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:49:35.702 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 20:49:35.702 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 20:49:35.702 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:49:35.703 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 20:49:35.703 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 20:49:35.703 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:49:35.704 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 20:49:35.704 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 20:49:35.706 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-06-04 20:49:35.707 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-06-04 20:49:35.708 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-06-04 20:49:35.712 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-06-04 20:49:35.714 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-06-04 20:49:35.719 [Information] BackupService: Initializing backup service
2025-06-04 20:49:35.720 [Information] BackupService: Backup service initialized successfully
2025-06-04 20:49:35.720 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-06-04 20:49:35.721 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-06-04 20:49:35.724 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-06-04 20:49:35.772 [Information] BackupService: Compressing backup data
2025-06-04 20:49:35.782 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (450 bytes)
2025-06-04 20:49:35.783 [Information] BackupServiceFactory: Created template for category: Production
2025-06-04 20:49:35.784 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-06-04 20:49:35.784 [Information] BackupService: Compressing backup data
2025-06-04 20:49:35.795 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (451 bytes)
2025-06-04 20:49:35.796 [Information] BackupServiceFactory: Created template for category: Development
2025-06-04 20:49:35.796 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-06-04 20:49:35.796 [Information] BackupService: Compressing backup data
2025-06-04 20:49:35.798 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (446 bytes)
2025-06-04 20:49:35.798 [Information] BackupServiceFactory: Created template for category: Testing
2025-06-04 20:49:35.798 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-06-04 20:49:35.799 [Information] BackupService: Compressing backup data
2025-06-04 20:49:35.800 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-06-04 20:49:35.800 [Information] BackupServiceFactory: Created template for category: Archived
2025-06-04 20:49:35.801 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-06-04 20:49:35.801 [Information] BackupService: Compressing backup data
2025-06-04 20:49:35.802 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (449 bytes)
2025-06-04 20:49:35.802 [Information] BackupServiceFactory: Created template for category: Critical
2025-06-04 20:49:35.803 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-06-04 20:49:35.803 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-06-04 20:49:35.804 [Information] BackupService: Compressing backup data
2025-06-04 20:49:35.807 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (512 bytes)
2025-06-04 20:49:35.808 [Information] BackupServiceFactory: Created template with predefined tags
2025-06-04 20:49:35.808 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-06-04 20:49:35.810 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-06-04 20:49:35.813 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 20:49:35.815 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 20:49:35.907 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-04 20:49:35.908 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 20:49:35.910 [Information] BackupSchedulerService: Starting backup scheduler
2025-06-04 20:49:35.910 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-06-04 20:49:35.911 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-06-04 20:49:35.912 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-06-04 20:49:35.913 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-06-04 20:49:35.918 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-06-04 20:49:35.918 [Information] App: Flash operation monitor service initialized successfully
2025-06-04 20:49:35.932 [Information] LicensingService: Initializing licensing service
2025-06-04 20:49:35.999 [Information] LicensingService: License information loaded successfully
2025-06-04 20:49:36.002 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-06-04 20:49:36.003 [Information] App: Licensing service initialized successfully
2025-06-04 20:49:36.003 [Information] App: License status: Trial
2025-06-04 20:49:36.004 [Information] App: Trial period: 30 days remaining
2025-06-04 20:49:36.005 [Information] BackupSchedulerService: Getting all backup schedules
2025-06-04 20:49:36.051 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-06-04 20:49:36.219 [Information] VocomService: Initializing Vocom service
2025-06-04 20:49:36.220 [Information] VocomService: Checking if PTT application is running
2025-06-04 20:49:36.235 [Information] VocomService: PTT application is not running
2025-06-04 20:49:36.235 [Debug] VocomService: Checking if Bluetooth is enabled
2025-06-04 20:49:36.236 [Debug] VocomService: Bluetooth is enabled
2025-06-04 20:49:36.237 [Information] VocomService: Vocom service initialized successfully
2025-06-04 20:49:36.288 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 20:49:36.289 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 20:49:36.289 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 20:49:36.290 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 20:49:36.290 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 20:49:36.292 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 20:49:36.292 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 20:49:36.294 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 20:49:36.295 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 20:49:36.295 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 20:49:36.307 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 20:49:36.308 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 20:49:36.308 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 20:49:36.308 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 20:49:36.309 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 20:49:36.309 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 20:49:36.309 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 20:49:36.309 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 20:49:36.310 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 20:49:36.310 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 20:49:36.310 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 20:49:36.310 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 20:49:36.311 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 20:49:36.311 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 20:49:36.311 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 20:49:36.311 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 20:49:36.312 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 20:49:36.312 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 20:49:36.318 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-06-04 20:49:36.319 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 20:49:36.319 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 20:49:36.320 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:49:36.326 [Information] CANRegisterAccess: Read value 0xD6 from register 0x0141 (simulated)
2025-06-04 20:49:36.332 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:49:36.339 [Information] CANRegisterAccess: Read value 0x3E from register 0x0141 (simulated)
2025-06-04 20:49:36.344 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:49:36.350 [Information] CANRegisterAccess: Read value 0xC1 from register 0x0141 (simulated)
2025-06-04 20:49:36.351 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-06-04 20:49:36.351 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-06-04 20:49:36.352 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-06-04 20:49:36.358 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-06-04 20:49:36.359 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-06-04 20:49:36.365 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-06-04 20:49:36.366 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-06-04 20:49:36.366 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-06-04 20:49:36.373 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-06-04 20:49:36.373 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-06-04 20:49:36.374 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-06-04 20:49:36.378 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-06-04 20:49:36.379 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-06-04 20:49:36.385 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-06-04 20:49:36.386 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-06-04 20:49:36.392 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-06-04 20:49:36.393 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-06-04 20:49:36.399 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-06-04 20:49:36.400 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-06-04 20:49:36.407 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-06-04 20:49:36.408 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-06-04 20:49:36.414 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-06-04 20:49:36.415 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-06-04 20:49:36.421 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-06-04 20:49:36.422 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-06-04 20:49:36.428 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-06-04 20:49:36.429 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-06-04 20:49:36.435 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-06-04 20:49:36.436 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-06-04 20:49:36.442 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-06-04 20:49:36.443 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-06-04 20:49:36.449 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-06-04 20:49:36.450 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-06-04 20:49:36.457 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-06-04 20:49:36.458 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-06-04 20:49:36.464 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-06-04 20:49:36.465 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-06-04 20:49:36.471 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-06-04 20:49:36.472 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-06-04 20:49:36.478 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-06-04 20:49:36.479 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-06-04 20:49:36.485 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-06-04 20:49:36.486 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-06-04 20:49:36.493 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-06-04 20:49:36.494 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-06-04 20:49:36.494 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-06-04 20:49:36.500 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-06-04 20:49:36.501 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-06-04 20:49:36.502 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-06-04 20:49:36.502 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:49:36.508 [Information] CANRegisterAccess: Read value 0x79 from register 0x0141 (simulated)
2025-06-04 20:49:36.513 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:49:36.519 [Information] CANRegisterAccess: Read value 0x97 from register 0x0141 (simulated)
2025-06-04 20:49:36.525 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:49:36.531 [Information] CANRegisterAccess: Read value 0x0E from register 0x0141 (simulated)
2025-06-04 20:49:36.532 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-06-04 20:49:36.532 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-06-04 20:49:36.533 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-06-04 20:49:36.533 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-06-04 20:49:36.538 [Information] CANRegisterAccess: Read value 0x06 from register 0x0140 (simulated)
2025-06-04 20:49:36.544 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-06-04 20:49:36.550 [Information] CANRegisterAccess: Read value 0xB0 from register 0x0140 (simulated)
2025-06-04 20:49:36.551 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-06-04 20:49:36.551 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 20:49:36.552 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 20:49:36.552 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 20:49:36.563 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 20:49:36.564 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 20:49:36.564 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 20:49:36.564 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:49:36.565 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-06-04 20:49:36.615 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-06-04 20:49:36.616 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 20:49:36.616 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 20:49:36.617 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 20:49:36.617 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 20:49:36.628 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 20:49:36.629 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 20:49:36.629 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 20:49:36.641 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 20:49:36.652 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 20:49:36.663 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 20:49:36.674 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 20:49:36.685 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 20:49:36.686 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 20:49:36.686 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 20:49:36.697 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 20:49:36.698 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 20:49:36.698 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 20:49:36.709 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 20:49:36.720 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 20:49:36.731 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 20:49:36.742 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 20:49:36.753 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 20:49:36.764 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 20:49:36.765 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 20:49:36.765 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 20:49:36.776 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 20:49:36.777 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 20:49:36.777 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 20:49:36.777 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 20:49:36.777 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 20:49:36.778 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 20:49:36.778 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 20:49:36.778 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 20:49:36.779 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 20:49:36.779 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 20:49:36.779 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 20:49:36.779 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 20:49:36.780 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 20:49:36.780 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 20:49:36.780 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 20:49:36.780 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 20:49:36.781 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 20:49:36.882 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 20:49:36.882 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 20:49:36.883 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 20:49:36.883 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:49:36.884 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 20:49:36.884 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 20:49:36.884 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:49:36.885 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 20:49:36.885 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 20:49:36.885 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:49:36.886 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 20:49:36.886 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 20:49:36.886 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:49:36.886 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 20:49:36.887 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 20:49:36.938 [Information] BackupService: Initializing backup service
2025-06-04 20:49:36.939 [Information] BackupService: Backup service initialized successfully
2025-06-04 20:49:36.990 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 20:49:36.991 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 20:49:36.994 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-04 20:49:36.994 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 20:49:37.046 [Information] BackupService: Getting predefined backup categories
2025-06-04 20:49:37.098 [Information] MainViewModel: Services initialized successfully
2025-06-04 20:49:37.131 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 20:49:37.132 [Information] VocomService: Scanning for Vocom devices
2025-06-04 20:49:37.133 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 20:49:37.134 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 20:49:37.134 [Debug] VocomService: Checking if Bluetooth is enabled
2025-06-04 20:49:37.134 [Debug] VocomService: Bluetooth is enabled
2025-06-04 20:49:37.135 [Debug] VocomService: Checking if WiFi is available
2025-06-04 20:49:37.159 [Debug] VocomService: WiFi is available
2025-06-04 20:49:37.160 [Information] VocomService: Found 2 Vocom devices
2025-06-04 20:49:37.161 [Information] MainViewModel: Found 2 Vocom device(s)
