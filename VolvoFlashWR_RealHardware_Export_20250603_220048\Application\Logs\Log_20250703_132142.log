Log started at 7/3/2025 1:21:42 PM
2025-07-03 13:21:42.294 [Information] LoggingService: Logging service initialized
2025-07-03 13:21:42.338 [Information] App: Verbose logging enabled
2025-07-03 13:21:42.340 [Information] AppConfigurationService: Initializing configuration service
2025-07-03 13:21:42.341 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Config
2025-07-03 13:21:42.434 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Config\app_config.json
2025-07-03 13:21:42.436 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-03 13:21:42.436 [Information] App: Configuration service initialized successfully
2025-07-03 13:21:42.438 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-03 13:21:42.438 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-03 13:21:42.446 [Information] App: Environment variable exists: True, not 'false': False
2025-07-03 13:21:42.446 [Information] App: Final useDummyImplementations value: False
2025-07-03 13:21:42.447 [Information] App: Updating config to NOT use dummy implementations
2025-07-03 13:21:42.449 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-03 13:21:42.470 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Config\app_config.json
2025-07-03 13:21:42.472 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-03 13:21:42.472 [Information] App: usePatchedImplementation flag is: True
2025-07-03 13:21:42.473 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-03 13:21:42.473 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries'
2025-07-03 13:21:42.473 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-03 13:21:42.474 [Information] App: verboseLogging flag is: True
2025-07-03 13:21:42.493 [Information] App: Verifying real hardware requirements...
2025-07-03 13:21:42.500 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-03 13:21:42.500 [Information] App: ✓ Found critical library: apci.dll
2025-07-03 13:21:42.500 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-03 13:21:42.501 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-03 13:21:42.501 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 13:21:42.502 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-03 13:21:42.502 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Vocom\config.json
2025-07-03 13:21:42.503 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-03 13:21:42.515 [Information] App: *** ATTEMPTING TO CREATE PATCHED VOCOM SERVICE FACTORY ***
2025-07-03 13:21:42.516 [Information] App: Found PatchedVocomServiceFactory type: VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
2025-07-03 13:21:42.517 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-03 13:21:42.519 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
2025-07-03 13:21:42.519 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\VolvoFlashWR.Communication.dll
2025-07-03 13:21:42.546 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-03 13:21:42.547 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\patched_factory_created.txt
2025-07-03 13:21:42.547 [Information] App: Successfully created PatchedVocomServiceFactory instance using reflection
2025-07-03 13:21:42.547 [Information] App: Using VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory Vocom service factory
2025-07-03 13:21:42.548 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-03 13:21:42.601 [Information] App: Creating Vocom service (attempt 1/3)
2025-07-03 13:21:42.605 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-03 13:21:42.605 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-03 13:21:42.606 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-03 13:21:42.606 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-03 13:21:42.606 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-03 13:21:42.609 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-03 13:21:42.612 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-03 13:21:42.619 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-03 13:21:42.621 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-03 13:21:42.622 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-03 13:21:42.640 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-03 13:21:42.664 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-03 13:21:42.665 [Information] PhoenixVocomAdapter: Loading APCI library dynamically
2025-07-03 13:21:42.667 [Error] PhoenixVocomAdapter: Failed to load APCI library. Error code: 193
2025-07-03 13:21:42.668 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-03 13:21:42.668 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-03 13:21:42.670 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-03 13:21:42.674 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-03 13:21:42.675 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-07-03 13:21:42.677 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-03 13:21:42.679 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-03 13:21:42.681 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 13:21:42.682 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-03 13:21:42.682 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-03 13:21:42.683 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-03 13:21:42.685 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-03 13:21:42.685 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-03 13:21:42.686 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-03 13:21:42.686 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-03 13:21:42.698 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-03 13:21:42.699 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-03 13:21:42.700 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-03 13:21:42.701 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-07-03 13:21:42.703 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-07-03 13:21:42.706 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-03 13:21:42.708 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-03 13:21:42.709 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-03 13:21:42.709 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-03 13:21:42.710 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-03 13:21:42.711 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-03 13:21:42.711 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-03 13:21:42.714 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-03 13:21:42.717 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-03 13:21:42.718 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-03 13:21:42.723 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-03 13:21:42.723 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-03 13:21:42.724 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\apci.dll
2025-07-03 13:21:42.724 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\apci.dll
2025-07-03 13:21:42.727 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-03 13:21:42.728 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-07-03 13:21:42.729 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-07-03 13:21:42.730 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-03 13:21:42.730 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-07-03 13:21:42.731 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-07-03 13:21:42.732 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-03 13:21:42.733 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-07-03 13:21:42.734 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-03 13:21:42.734 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-07-03 13:21:42.735 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-03 13:21:42.736 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-07-03 13:21:42.736 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-07-03 13:21:42.737 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-03 13:21:42.737 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-07-03 13:21:42.738 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-07-03 13:21:42.738 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-03 13:21:42.739 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\
2025-07-03 13:21:42.739 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\
2025-07-03 13:21:42.740 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-07-03 13:21:42.741 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-07-03 13:21:42.741 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-03 13:21:42.742 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries
2025-07-03 13:21:42.742 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-07-03 13:21:42.743 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-03 13:21:42.743 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-07-03 13:21:42.744 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-07-03 13:21:42.744 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-03 13:21:42.745 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-07-03 13:21:42.745 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-07-03 13:21:42.746 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-03 13:21:42.747 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\
2025-07-03 13:21:42.748 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\
2025-07-03 13:21:42.749 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\
2025-07-03 13:21:42.753 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-03 13:21:42.754 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\apci.dll
2025-07-03 13:21:42.754 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-07-03 13:21:42.755 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-03 13:21:42.756 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-03 13:21:42.758 [Information] VocomDriver: Initializing Vocom driver
2025-07-03 13:21:42.760 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-03 13:21:42.764 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-03 13:21:42.765 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 13:21:42.765 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 13:21:42.767 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 13:21:42.769 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-07-03 13:21:42.772 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\msvcr120.dll
2025-07-03 13:21:42.773 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\msvcp120.dll
2025-07-03 13:21:42.776 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-03 13:21:42.777 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\msvcp140.dll
2025-07-03 13:21:42.779 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\vcruntime140.dll
2025-07-03 13:21:42.779 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-03 13:21:42.784 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-07-03 13:21:42.785 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-07-03 13:21:42.786 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-07-03 13:21:42.787 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-03 13:21:42.788 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-03 13:21:42.789 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-03 13:21:42.791 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-03 13:21:42.792 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-03 13:21:42.792 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-03 13:21:42.793 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-03 13:21:42.793 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-03 13:21:42.793 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-03 13:21:42.793 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-03 13:21:42.794 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-03 13:21:42.794 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-03 13:21:42.794 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-03 13:21:42.795 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-03 13:21:42.795 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-03 13:21:42.795 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-03 13:21:42.796 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-03 13:21:42.796 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-03 13:21:42.797 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-03 13:21:42.797 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-03 13:21:42.797 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-03 13:21:42.798 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-03 13:21:42.798 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-03 13:21:42.799 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-03 13:21:42.799 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-03 13:21:42.799 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-03 13:21:42.800 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-03 13:21:42.800 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-03 13:21:42.800 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-03 13:21:42.801 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-03 13:21:42.801 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-03 13:21:42.801 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-03 13:21:42.802 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-03 13:21:42.802 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-03 13:21:42.802 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-03 13:21:42.802 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-03 13:21:42.803 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-03 13:21:42.803 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-03 13:21:42.803 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-03 13:21:42.804 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-03 13:21:42.804 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-03 13:21:42.804 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-03 13:21:42.805 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-03 13:21:42.805 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-03 13:21:42.805 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-03 13:21:42.806 [Warning] WUDFPumaDependencyResolver: Function WUDF_Initialize not found in WUDFPuma.dll
2025-07-03 13:21:42.806 [Warning] WUDFPumaDependencyResolver: Function VocomInitialize not found in WUDFPuma.dll
2025-07-03 13:21:42.807 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-03 13:21:42.807 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-03 13:21:42.807 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-03 13:21:42.808 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-03 13:21:42.808 [Warning] VocomNativeInterop: Could not find any of these WUDFPuma functions: WUDF_Initialize, VocomInitialize, Initialize, DllMain, WUDFDriverEntry, DriverEntry
2025-07-03 13:21:42.808 [Warning] WUDFPumaDependencyResolver: Function WUDF_Shutdown not found in WUDFPuma.dll
2025-07-03 13:21:42.808 [Warning] WUDFPumaDependencyResolver: Function VocomShutdown not found in WUDFPuma.dll
2025-07-03 13:21:42.809 [Warning] WUDFPumaDependencyResolver: Function Shutdown not found in WUDFPuma.dll
2025-07-03 13:21:42.809 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-03 13:21:42.809 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-03 13:21:42.810 [Warning] VocomNativeInterop: Could not find any of these WUDFPuma functions: WUDF_Shutdown, VocomShutdown, Shutdown, Cleanup, WUDFDriverUnload
2025-07-03 13:21:42.810 [Warning] WUDFPumaDependencyResolver: Function WUDF_DetectDevices not found in WUDFPuma.dll
2025-07-03 13:21:42.810 [Warning] WUDFPumaDependencyResolver: Function VocomDetectDevices not found in WUDFPuma.dll
2025-07-03 13:21:42.810 [Warning] WUDFPumaDependencyResolver: Function DetectDevices not found in WUDFPuma.dll
2025-07-03 13:21:42.811 [Warning] WUDFPumaDependencyResolver: Function EnumerateDevices not found in WUDFPuma.dll
2025-07-03 13:21:42.811 [Warning] WUDFPumaDependencyResolver: Function GetDevices not found in WUDFPuma.dll
2025-07-03 13:21:42.811 [Warning] VocomNativeInterop: Could not find any of these WUDFPuma functions: WUDF_DetectDevices, VocomDetectDevices, DetectDevices, EnumerateDevices, GetDevices
2025-07-03 13:21:42.812 [Warning] WUDFPumaDependencyResolver: Function WUDF_ConnectDevice not found in WUDFPuma.dll
2025-07-03 13:21:42.812 [Warning] WUDFPumaDependencyResolver: Function VocomConnectDevice not found in WUDFPuma.dll
2025-07-03 13:21:42.812 [Warning] WUDFPumaDependencyResolver: Function ConnectDevice not found in WUDFPuma.dll
2025-07-03 13:21:42.813 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-03 13:21:42.813 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-03 13:21:42.813 [Warning] VocomNativeInterop: Could not find any of these WUDFPuma functions: WUDF_ConnectDevice, VocomConnectDevice, ConnectDevice, OpenDevice, Connect
2025-07-03 13:21:42.814 [Warning] WUDFPumaDependencyResolver: Function WUDF_DisconnectDevice not found in WUDFPuma.dll
2025-07-03 13:21:42.814 [Warning] WUDFPumaDependencyResolver: Function VocomDisconnectDevice not found in WUDFPuma.dll
2025-07-03 13:21:42.814 [Warning] WUDFPumaDependencyResolver: Function DisconnectDevice not found in WUDFPuma.dll
2025-07-03 13:21:42.815 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-03 13:21:42.815 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-03 13:21:42.816 [Warning] VocomNativeInterop: Could not find any of these WUDFPuma functions: WUDF_DisconnectDevice, VocomDisconnectDevice, DisconnectDevice, CloseDevice, Disconnect
2025-07-03 13:21:42.816 [Warning] WUDFPumaDependencyResolver: Function WUDF_SendCANFrame not found in WUDFPuma.dll
2025-07-03 13:21:42.817 [Warning] WUDFPumaDependencyResolver: Function VocomSendCANFrame not found in WUDFPuma.dll
2025-07-03 13:21:42.817 [Warning] WUDFPumaDependencyResolver: Function SendCANFrame not found in WUDFPuma.dll
2025-07-03 13:21:42.817 [Warning] WUDFPumaDependencyResolver: Function SendCAN not found in WUDFPuma.dll
2025-07-03 13:21:42.818 [Warning] WUDFPumaDependencyResolver: Function TransmitCAN not found in WUDFPuma.dll
2025-07-03 13:21:42.818 [Warning] WUDFPumaDependencyResolver: Function WriteData not found in WUDFPuma.dll
2025-07-03 13:21:42.818 [Warning] VocomNativeInterop: Could not find any of these WUDFPuma functions: WUDF_SendCANFrame, VocomSendCANFrame, SendCANFrame, SendCAN, TransmitCAN, WriteData
2025-07-03 13:21:42.819 [Warning] WUDFPumaDependencyResolver: Function WUDF_SendData not found in WUDFPuma.dll
2025-07-03 13:21:42.819 [Warning] WUDFPumaDependencyResolver: Function VocomSendData not found in WUDFPuma.dll
2025-07-03 13:21:42.819 [Warning] WUDFPumaDependencyResolver: Function SendData not found in WUDFPuma.dll
2025-07-03 13:21:42.819 [Warning] WUDFPumaDependencyResolver: Function WriteData not found in WUDFPuma.dll
2025-07-03 13:21:42.820 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-03 13:21:42.820 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-03 13:21:42.820 [Warning] VocomNativeInterop: Could not find any of these WUDFPuma functions: WUDF_SendData, VocomSendData, SendData, WriteData, Transmit, Send
2025-07-03 13:21:42.821 [Warning] VocomNativeInterop: Could not load essential WUDFPuma functions
2025-07-03 13:21:42.821 [Warning] VocomNativeInterop: Failed to load WUDFPuma function pointers, falling back to modern implementation
2025-07-03 13:21:42.821 [Warning] VocomNativeInterop: Failed to load WUDFPuma.dll or function pointers, falling back to modern .NET 8.0 implementation
2025-07-03 13:21:42.822 [Information] VocomNativeInterop: Using modern .NET 8.0 implementation for Vocom communication
2025-07-03 13:21:42.827 [Information] VocomNativeInterop: Initializing HID device detection for Vocom adapters
2025-07-03 13:21:42.870 [Information] VocomNativeInterop: No HID Vocom devices found
2025-07-03 13:21:42.874 [Information] VocomNativeInterop: Initializing serial port detection for Vocom adapters
2025-07-03 13:21:42.875 [Information] VocomNativeInterop: No serial ports found
2025-07-03 13:21:42.875 [Warning] VocomNativeInterop: Failed to initialize modern Vocom communication, no devices found
2025-07-03 13:21:42.876 [Error] VocomNativeInterop: Failed to initialize Vocom driver
2025-07-03 13:21:42.877 [Error] VocomDriver: Failed to initialize Vocom native interop
2025-07-03 13:21:42.878 [Warning] PatchedVocomServiceFactory: Failed to initialize standard Vocom driver, falling back to device driver
2025-07-03 13:21:42.880 [Information] VocomDeviceDriver: Initializing Vocom device driver
2025-07-03 13:21:42.880 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-03 13:21:42.880 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-03 13:21:42.881 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 13:21:42.881 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 13:21:42.882 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 13:21:42.882 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-07-03 13:21:42.883 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\msvcr120.dll
2025-07-03 13:21:42.884 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\msvcp120.dll
2025-07-03 13:21:42.886 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-03 13:21:42.886 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\msvcp140.dll
2025-07-03 13:21:42.887 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\vcruntime140.dll
2025-07-03 13:21:42.888 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-03 13:21:42.888 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-07-03 13:21:42.888 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-07-03 13:21:42.889 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-07-03 13:21:42.889 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-03 13:21:42.889 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-03 13:21:42.889 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-03 13:21:42.890 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-03 13:21:42.890 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-03 13:21:42.890 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-03 13:21:42.891 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-03 13:21:42.891 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-03 13:21:42.891 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-03 13:21:42.891 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-03 13:21:42.892 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-03 13:21:42.892 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-03 13:21:42.892 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-03 13:21:42.892 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-03 13:21:42.893 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-03 13:21:42.893 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-03 13:21:42.893 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-03 13:21:42.893 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-03 13:21:42.894 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-03 13:21:42.894 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-03 13:21:42.894 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-03 13:21:42.895 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-03 13:21:42.895 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-03 13:21:42.895 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-03 13:21:42.896 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-03 13:21:42.896 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-03 13:21:42.896 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-03 13:21:42.897 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-03 13:21:42.897 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-03 13:21:42.897 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-03 13:21:42.898 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-03 13:21:42.898 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-03 13:21:42.898 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-03 13:21:42.899 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-03 13:21:42.899 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-03 13:21:42.899 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-03 13:21:42.900 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-03 13:21:42.900 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-03 13:21:42.900 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-03 13:21:42.900 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-03 13:21:42.901 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-03 13:21:42.901 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-03 13:21:42.901 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-03 13:21:42.902 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-03 13:21:42.902 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-03 13:21:42.902 [Warning] WUDFPumaDependencyResolver: Function WUDF_Initialize not found in WUDFPuma.dll
2025-07-03 13:21:42.902 [Warning] WUDFPumaDependencyResolver: Function VocomInitialize not found in WUDFPuma.dll
2025-07-03 13:21:42.903 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-03 13:21:42.903 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-03 13:21:42.903 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-03 13:21:42.903 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-03 13:21:42.904 [Warning] VocomNativeInterop: Could not find any of these WUDFPuma functions: WUDF_Initialize, VocomInitialize, Initialize, DllMain, WUDFDriverEntry, DriverEntry
2025-07-03 13:21:42.904 [Warning] WUDFPumaDependencyResolver: Function WUDF_Shutdown not found in WUDFPuma.dll
2025-07-03 13:21:42.904 [Warning] WUDFPumaDependencyResolver: Function VocomShutdown not found in WUDFPuma.dll
2025-07-03 13:21:42.904 [Warning] WUDFPumaDependencyResolver: Function Shutdown not found in WUDFPuma.dll
2025-07-03 13:21:42.905 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-03 13:21:42.905 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-03 13:21:42.905 [Warning] VocomNativeInterop: Could not find any of these WUDFPuma functions: WUDF_Shutdown, VocomShutdown, Shutdown, Cleanup, WUDFDriverUnload
2025-07-03 13:21:42.906 [Warning] WUDFPumaDependencyResolver: Function WUDF_DetectDevices not found in WUDFPuma.dll
2025-07-03 13:21:42.906 [Warning] WUDFPumaDependencyResolver: Function VocomDetectDevices not found in WUDFPuma.dll
2025-07-03 13:21:42.906 [Warning] WUDFPumaDependencyResolver: Function DetectDevices not found in WUDFPuma.dll
2025-07-03 13:21:42.906 [Warning] WUDFPumaDependencyResolver: Function EnumerateDevices not found in WUDFPuma.dll
2025-07-03 13:21:42.907 [Warning] WUDFPumaDependencyResolver: Function GetDevices not found in WUDFPuma.dll
2025-07-03 13:21:42.907 [Warning] VocomNativeInterop: Could not find any of these WUDFPuma functions: WUDF_DetectDevices, VocomDetectDevices, DetectDevices, EnumerateDevices, GetDevices
2025-07-03 13:21:42.907 [Warning] WUDFPumaDependencyResolver: Function WUDF_ConnectDevice not found in WUDFPuma.dll
2025-07-03 13:21:42.908 [Warning] WUDFPumaDependencyResolver: Function VocomConnectDevice not found in WUDFPuma.dll
2025-07-03 13:21:42.908 [Warning] WUDFPumaDependencyResolver: Function ConnectDevice not found in WUDFPuma.dll
2025-07-03 13:21:42.908 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-03 13:21:42.908 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-03 13:21:42.909 [Warning] VocomNativeInterop: Could not find any of these WUDFPuma functions: WUDF_ConnectDevice, VocomConnectDevice, ConnectDevice, OpenDevice, Connect
2025-07-03 13:21:42.909 [Warning] WUDFPumaDependencyResolver: Function WUDF_DisconnectDevice not found in WUDFPuma.dll
2025-07-03 13:21:42.909 [Warning] WUDFPumaDependencyResolver: Function VocomDisconnectDevice not found in WUDFPuma.dll
2025-07-03 13:21:42.909 [Warning] WUDFPumaDependencyResolver: Function DisconnectDevice not found in WUDFPuma.dll
2025-07-03 13:21:42.910 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-03 13:21:42.910 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-03 13:21:42.910 [Warning] VocomNativeInterop: Could not find any of these WUDFPuma functions: WUDF_DisconnectDevice, VocomDisconnectDevice, DisconnectDevice, CloseDevice, Disconnect
2025-07-03 13:21:42.910 [Warning] WUDFPumaDependencyResolver: Function WUDF_SendCANFrame not found in WUDFPuma.dll
2025-07-03 13:21:42.911 [Warning] WUDFPumaDependencyResolver: Function VocomSendCANFrame not found in WUDFPuma.dll
2025-07-03 13:21:42.911 [Warning] WUDFPumaDependencyResolver: Function SendCANFrame not found in WUDFPuma.dll
2025-07-03 13:21:42.911 [Warning] WUDFPumaDependencyResolver: Function SendCAN not found in WUDFPuma.dll
2025-07-03 13:21:42.911 [Warning] WUDFPumaDependencyResolver: Function TransmitCAN not found in WUDFPuma.dll
2025-07-03 13:21:42.912 [Warning] WUDFPumaDependencyResolver: Function WriteData not found in WUDFPuma.dll
2025-07-03 13:21:42.912 [Warning] VocomNativeInterop: Could not find any of these WUDFPuma functions: WUDF_SendCANFrame, VocomSendCANFrame, SendCANFrame, SendCAN, TransmitCAN, WriteData
2025-07-03 13:21:42.912 [Warning] WUDFPumaDependencyResolver: Function WUDF_SendData not found in WUDFPuma.dll
2025-07-03 13:21:42.912 [Warning] WUDFPumaDependencyResolver: Function VocomSendData not found in WUDFPuma.dll
2025-07-03 13:21:42.913 [Warning] WUDFPumaDependencyResolver: Function SendData not found in WUDFPuma.dll
2025-07-03 13:21:42.913 [Warning] WUDFPumaDependencyResolver: Function WriteData not found in WUDFPuma.dll
2025-07-03 13:21:42.913 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-03 13:21:42.914 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-03 13:21:42.914 [Warning] VocomNativeInterop: Could not find any of these WUDFPuma functions: WUDF_SendData, VocomSendData, SendData, WriteData, Transmit, Send
2025-07-03 13:21:42.915 [Warning] VocomNativeInterop: Could not load essential WUDFPuma functions
2025-07-03 13:21:42.915 [Warning] VocomNativeInterop: Failed to load WUDFPuma function pointers, falling back to modern implementation
2025-07-03 13:21:42.915 [Warning] VocomNativeInterop: Failed to load WUDFPuma.dll or function pointers, falling back to modern .NET 8.0 implementation
2025-07-03 13:21:42.917 [Information] VocomNativeInterop: Using modern .NET 8.0 implementation for Vocom communication
2025-07-03 13:21:42.917 [Information] VocomNativeInterop: Initializing HID device detection for Vocom adapters
2025-07-03 13:21:42.918 [Information] VocomNativeInterop: No HID Vocom devices found
2025-07-03 13:21:42.919 [Information] VocomNativeInterop: Initializing serial port detection for Vocom adapters
2025-07-03 13:21:42.919 [Information] VocomNativeInterop: No serial ports found
2025-07-03 13:21:42.919 [Warning] VocomNativeInterop: Failed to initialize modern Vocom communication, no devices found
2025-07-03 13:21:42.920 [Error] VocomNativeInterop: Failed to initialize Vocom driver
2025-07-03 13:21:42.921 [Error] VocomDeviceDriver: Failed to initialize Vocom native interop layer
2025-07-03 13:21:42.921 [Error] PatchedVocomServiceFactory: Failed to initialize Vocom device driver, falling back to dummy implementation
2025-07-03 13:21:42.924 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-03 13:21:42.924 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-03 13:21:42.924 [Information] App: Initializing Vocom service
2025-07-03 13:21:42.925 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-03 13:21:42.925 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-03 13:21:42.926 [Information] DummyVocomService: Scanning for Vocom devices (dummy)
2025-07-03 13:21:43.027 [Information] DummyVocomService: Found 1 Vocom devices (dummy)
2025-07-03 13:21:43.028 [Information] App: Found 1 Vocom devices, attempting to connect to the first one
2025-07-03 13:21:43.030 [Information] DummyVocomService: Connecting to Vocom device Dummy Vocom Device (dummy)
2025-07-03 13:21:43.230 [Information] DummyVocomService: Connected to Vocom device Dummy Vocom Device (dummy)
2025-07-03 13:21:43.231 [Information] App: Connected to Vocom device Dummy Vocom Device
2025-07-03 13:21:43.235 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-03 13:21:43.238 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 13:21:43.239 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-03 13:21:43.242 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-03 13:21:43.244 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-03 13:21:43.245 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-03 13:21:43.247 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-03 13:21:43.250 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-03 13:21:43.253 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-03 13:21:43.256 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-03 13:21:43.258 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-03 13:21:43.266 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-03 13:21:43.269 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-03 13:21:43.282 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-03 13:21:43.283 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-03 13:21:43.284 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-03 13:21:43.284 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-03 13:21:43.285 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-03 13:21:43.285 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-03 13:21:43.285 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-03 13:21:43.286 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-03 13:21:43.286 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-03 13:21:43.290 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-03 13:21:43.290 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-03 13:21:43.290 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-03 13:21:43.291 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-03 13:21:43.291 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-03 13:21:43.291 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-03 13:21:43.292 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-03 13:21:43.292 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-03 13:21:43.295 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-03 13:21:43.302 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-03 13:21:43.303 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-03 13:21:43.307 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-03 13:21:43.309 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 13:21:43.317 [Information] CANRegisterAccess: Read value 0x97 from register 0x0141 (simulated)
2025-07-03 13:21:43.318 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-03 13:21:43.319 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-03 13:21:43.320 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-03 13:21:43.326 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-03 13:21:43.326 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-03 13:21:43.332 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-03 13:21:43.332 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-03 13:21:43.333 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-03 13:21:43.338 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-03 13:21:43.338 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-03 13:21:43.339 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-03 13:21:43.345 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-03 13:21:43.345 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-03 13:21:43.351 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-03 13:21:43.351 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-03 13:21:43.357 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-03 13:21:43.357 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-03 13:21:43.363 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-03 13:21:43.363 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-03 13:21:43.370 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-03 13:21:43.370 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-03 13:21:43.376 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-03 13:21:43.376 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-03 13:21:43.382 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-03 13:21:43.383 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-03 13:21:43.389 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-03 13:21:43.389 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-03 13:21:43.395 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-03 13:21:43.395 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-03 13:21:43.401 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-03 13:21:43.402 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-03 13:21:43.407 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-03 13:21:43.407 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-03 13:21:43.413 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-03 13:21:43.414 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-03 13:21:43.420 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-03 13:21:43.420 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-03 13:21:43.426 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-03 13:21:43.426 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-03 13:21:43.433 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-03 13:21:43.433 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-03 13:21:43.440 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-03 13:21:43.440 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-03 13:21:43.446 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-03 13:21:43.446 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-03 13:21:43.447 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-03 13:21:43.453 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-03 13:21:43.453 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-03 13:21:43.454 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-03 13:21:43.454 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 13:21:43.460 [Information] CANRegisterAccess: Read value 0xD4 from register 0x0141 (simulated)
2025-07-03 13:21:43.460 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-03 13:21:43.461 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-03 13:21:43.461 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-03 13:21:43.461 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-03 13:21:43.467 [Information] CANRegisterAccess: Read value 0x61 from register 0x0140 (simulated)
2025-07-03 13:21:43.473 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-03 13:21:43.479 [Information] CANRegisterAccess: Read value 0xC2 from register 0x0140 (simulated)
2025-07-03 13:21:43.485 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-03 13:21:43.491 [Information] CANRegisterAccess: Read value 0x19 from register 0x0140 (simulated)
2025-07-03 13:21:43.491 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-03 13:21:43.492 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-03 13:21:43.495 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-03 13:21:43.496 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-03 13:21:43.507 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-03 13:21:43.508 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-03 13:21:43.509 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-03 13:21:43.513 [Information] DummyVocomService: Sending data and waiting for response (dummy)
2025-07-03 13:21:43.665 [Information] DummyVocomService: Sent 4 bytes and received 6 bytes response (dummy)
2025-07-03 13:21:43.667 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-03 13:21:43.667 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-03 13:21:43.670 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-03 13:21:43.670 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-03 13:21:43.681 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-03 13:21:43.682 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-03 13:21:43.683 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-03 13:21:43.693 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-03 13:21:43.704 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-03 13:21:43.715 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-03 13:21:43.726 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-03 13:21:43.737 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-03 13:21:43.739 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-03 13:21:43.740 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-03 13:21:43.751 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-03 13:21:43.752 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-03 13:21:43.752 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-03 13:21:43.763 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-03 13:21:43.774 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-03 13:21:43.785 [Information] IICProtocolHandler: Enabling IIC module
2025-07-03 13:21:43.796 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-03 13:21:43.807 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-03 13:21:43.818 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-03 13:21:43.820 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-03 13:21:43.821 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-03 13:21:43.832 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-03 13:21:43.834 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-03 13:21:43.834 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-03 13:21:43.834 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-03 13:21:43.835 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-03 13:21:43.835 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-03 13:21:43.835 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-03 13:21:43.836 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-03 13:21:43.836 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-03 13:21:43.836 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-03 13:21:43.837 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-03 13:21:43.837 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-03 13:21:43.837 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-03 13:21:43.838 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-03 13:21:43.838 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-03 13:21:43.838 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-03 13:21:43.838 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-03 13:21:43.939 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-03 13:21:43.939 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-03 13:21:43.944 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-03 13:21:43.945 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 13:21:43.946 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-03 13:21:43.946 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-03 13:21:43.946 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 13:21:43.947 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-03 13:21:43.947 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-03 13:21:43.948 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 13:21:43.949 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-03 13:21:43.949 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-03 13:21:43.950 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 13:21:43.950 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-03 13:21:43.951 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-03 13:21:43.952 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-03 13:21:43.953 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-03 13:21:43.954 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-03 13:21:43.958 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-03 13:21:43.960 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-03 13:21:43.967 [Information] BackupService: Initializing backup service
2025-07-03 13:21:43.967 [Information] BackupService: Backup service initialized successfully
2025-07-03 13:21:43.968 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-03 13:21:43.968 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-03 13:21:43.971 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-03 13:21:44.027 [Information] BackupService: Compressing backup data
2025-07-03 13:21:44.037 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (451 bytes)
2025-07-03 13:21:44.038 [Information] BackupServiceFactory: Created template for category: Production
2025-07-03 13:21:44.039 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-03 13:21:44.039 [Information] BackupService: Compressing backup data
2025-07-03 13:21:44.041 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (450 bytes)
2025-07-03 13:21:44.041 [Information] BackupServiceFactory: Created template for category: Development
2025-07-03 13:21:44.042 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-03 13:21:44.042 [Information] BackupService: Compressing backup data
2025-07-03 13:21:44.043 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (448 bytes)
2025-07-03 13:21:44.044 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-03 13:21:44.044 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-03 13:21:44.044 [Information] BackupService: Compressing backup data
2025-07-03 13:21:44.046 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (451 bytes)
2025-07-03 13:21:44.047 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-03 13:21:44.047 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-03 13:21:44.048 [Information] BackupService: Compressing backup data
2025-07-03 13:21:44.052 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (449 bytes)
2025-07-03 13:21:44.053 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-03 13:21:44.053 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-03 13:21:44.054 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-03 13:21:44.054 [Information] BackupService: Compressing backup data
2025-07-03 13:21:44.056 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (514 bytes)
2025-07-03 13:21:44.056 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-03 13:21:44.056 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-03 13:21:44.058 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-03 13:21:44.062 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-03 13:21:44.064 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-03 13:21:44.138 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-07-03 13:21:44.139 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-03 13:21:44.141 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-03 13:21:44.142 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-03 13:21:44.142 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-03 13:21:44.143 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-03 13:21:44.144 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-03 13:21:44.149 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-03 13:21:44.150 [Information] App: Flash operation monitor service initialized successfully
2025-07-03 13:21:44.161 [Information] LicensingService: Initializing licensing service
2025-07-03 13:21:44.237 [Information] LicensingService: License information loaded successfully
2025-07-03 13:21:44.240 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-03 13:21:44.241 [Information] App: Licensing service initialized successfully
2025-07-03 13:21:44.241 [Information] App: License status: Trial
2025-07-03 13:21:44.242 [Information] App: Trial period: 1 days remaining
2025-07-03 13:21:44.243 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-03 13:21:44.272 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-07-03 13:21:44.429 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-03 13:21:44.430 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-03 13:21:44.480 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-03 13:21:44.481 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-03 13:21:44.481 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-03 13:21:44.482 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-03 13:21:44.482 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-03 13:21:44.486 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-03 13:21:44.486 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-03 13:21:44.488 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-03 13:21:44.488 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-03 13:21:44.489 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-03 13:21:44.499 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-03 13:21:44.500 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-03 13:21:44.500 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-03 13:21:44.501 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-03 13:21:44.501 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-03 13:21:44.501 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-03 13:21:44.502 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-03 13:21:44.502 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-03 13:21:44.502 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-03 13:21:44.503 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-03 13:21:44.503 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-03 13:21:44.503 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-03 13:21:44.503 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-03 13:21:44.504 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-03 13:21:44.504 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-03 13:21:44.504 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-03 13:21:44.505 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-03 13:21:44.505 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-03 13:21:44.511 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-03 13:21:44.512 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-03 13:21:44.512 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-03 13:21:44.513 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 13:21:44.518 [Information] CANRegisterAccess: Read value 0x45 from register 0x0141 (simulated)
2025-07-03 13:21:44.519 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-03 13:21:44.520 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-03 13:21:44.520 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-03 13:21:44.526 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-03 13:21:44.527 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-03 13:21:44.533 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-03 13:21:44.534 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-03 13:21:44.534 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-03 13:21:44.540 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-03 13:21:44.541 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-03 13:21:44.541 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-03 13:21:44.547 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-03 13:21:44.548 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-03 13:21:44.554 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-03 13:21:44.555 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-03 13:21:44.561 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-03 13:21:44.562 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-03 13:21:44.568 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-03 13:21:44.569 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-03 13:21:44.575 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-03 13:21:44.576 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-03 13:21:44.582 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-03 13:21:44.583 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-03 13:21:44.589 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-03 13:21:44.590 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-03 13:21:44.596 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-03 13:21:44.597 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-03 13:21:44.603 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-03 13:21:44.604 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-03 13:21:44.610 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-03 13:21:44.611 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-03 13:21:44.617 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-03 13:21:44.618 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-03 13:21:44.624 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-03 13:21:44.625 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-03 13:21:44.631 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-03 13:21:44.632 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-03 13:21:44.637 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-03 13:21:44.638 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-03 13:21:44.644 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-03 13:21:44.645 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-03 13:21:44.651 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-03 13:21:44.652 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-03 13:21:44.658 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-03 13:21:44.659 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-03 13:21:44.659 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-03 13:21:44.666 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-03 13:21:44.666 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-03 13:21:44.667 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-03 13:21:44.667 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 13:21:44.673 [Information] CANRegisterAccess: Read value 0x29 from register 0x0141 (simulated)
2025-07-03 13:21:44.679 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 13:21:44.685 [Information] CANRegisterAccess: Read value 0x68 from register 0x0141 (simulated)
2025-07-03 13:21:44.686 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-03 13:21:44.686 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-03 13:21:44.687 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-03 13:21:44.687 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-03 13:21:44.693 [Information] CANRegisterAccess: Read value 0x66 from register 0x0140 (simulated)
2025-07-03 13:21:44.699 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-03 13:21:44.705 [Information] CANRegisterAccess: Read value 0x46 from register 0x0140 (simulated)
2025-07-03 13:21:44.711 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-03 13:21:44.717 [Information] CANRegisterAccess: Read value 0x44 from register 0x0140 (simulated)
2025-07-03 13:21:44.724 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-03 13:21:44.730 [Information] CANRegisterAccess: Read value 0xE1 from register 0x0140 (simulated)
2025-07-03 13:21:44.736 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-03 13:21:44.742 [Information] CANRegisterAccess: Read value 0x4C from register 0x0140 (simulated)
2025-07-03 13:21:44.748 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-03 13:21:44.754 [Information] CANRegisterAccess: Read value 0x02 from register 0x0140 (simulated)
2025-07-03 13:21:44.760 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-03 13:21:44.767 [Information] CANRegisterAccess: Read value 0x19 from register 0x0140 (simulated)
2025-07-03 13:21:44.768 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-03 13:21:44.769 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-03 13:21:44.769 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-03 13:21:44.769 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-03 13:21:44.780 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-03 13:21:44.781 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-03 13:21:44.781 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-03 13:21:44.782 [Information] DummyVocomService: Sending data and waiting for response (dummy)
2025-07-03 13:21:44.933 [Information] DummyVocomService: Sent 4 bytes and received 6 bytes response (dummy)
2025-07-03 13:21:44.934 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-03 13:21:44.934 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-03 13:21:44.935 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-03 13:21:44.935 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-03 13:21:44.946 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-03 13:21:44.947 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-03 13:21:44.947 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-03 13:21:44.958 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-03 13:21:44.969 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-03 13:21:44.980 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-03 13:21:44.991 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-03 13:21:45.002 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-03 13:21:45.003 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-03 13:21:45.003 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-03 13:21:45.015 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-03 13:21:45.015 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-03 13:21:45.016 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-03 13:21:45.027 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-03 13:21:45.038 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-03 13:21:45.049 [Information] IICProtocolHandler: Enabling IIC module
2025-07-03 13:21:45.060 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-03 13:21:45.071 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-03 13:21:45.082 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-03 13:21:45.083 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-03 13:21:45.083 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-03 13:21:45.094 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-03 13:21:45.095 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-03 13:21:45.095 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-03 13:21:45.095 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-03 13:21:45.096 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-03 13:21:45.096 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-03 13:21:45.096 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-03 13:21:45.096 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-03 13:21:45.097 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-03 13:21:45.097 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-03 13:21:45.097 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-03 13:21:45.098 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-03 13:21:45.098 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-03 13:21:45.098 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-03 13:21:45.098 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-03 13:21:45.099 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-03 13:21:45.099 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-03 13:21:45.200 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-03 13:21:45.201 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-03 13:21:45.201 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-03 13:21:45.202 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 13:21:45.202 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-03 13:21:45.202 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-03 13:21:45.203 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 13:21:45.203 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-03 13:21:45.203 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-03 13:21:45.204 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 13:21:45.204 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-03 13:21:45.204 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-03 13:21:45.205 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 13:21:45.205 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-03 13:21:45.206 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-03 13:21:45.257 [Information] BackupService: Initializing backup service
2025-07-03 13:21:45.258 [Information] BackupService: Backup service initialized successfully
2025-07-03 13:21:45.308 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-03 13:21:45.309 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-03 13:21:45.310 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-07-03 13:21:45.311 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-03 13:21:45.362 [Information] BackupService: Getting predefined backup categories
2025-07-03 13:21:45.414 [Information] MainViewModel: Services initialized successfully
2025-07-03 13:21:45.418 [Information] MainViewModel: Scanning for Vocom devices
2025-07-03 13:21:45.420 [Information] DummyVocomService: Scanning for Vocom devices (dummy)
2025-07-03 13:21:45.521 [Information] DummyVocomService: Found 1 Vocom devices (dummy)
2025-07-03 13:21:45.523 [Information] MainViewModel: Found 1 Vocom device(s)
